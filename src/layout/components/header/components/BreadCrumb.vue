<template>
  <n-breadcrumb>
    <n-breadcrumb-item
      v-for="item in breadcrumbItems"
      :key="item.path"
      @click="handleBreadClick(item)"
    >
      <component :is="getIcon(item.meta)" />
      {{ item.meta.title }}
    </n-breadcrumb-item>
  </n-breadcrumb>
</template>

<script setup>
import { computed } from 'vue'
import { renderCustomIcon, renderIcon } from '@/utils'

const router = useRouter()
const route = useRoute()

// 计算面包屑项目
const breadcrumbItems = computed(() => {
  const matched = route.matched.filter((item) => !!item.meta?.title)

  // 特殊处理：如果当前页面是详情页面，需要构建正确的面包屑层级
  if (route.path.includes('/detail/')) {
    const breadcrumbs = []

    // 根据路径构建面包屑
    if (route.path.includes('/test_plan/functional_test_plan/detail/')) {
      // 功能测试计划详情页面
      breadcrumbs.push({
        path: '/test_plan/functional_test_plan',
        meta: { title: '功能测试计划', icon: 'material-symbols:assignment' },
        clickable: true
      })
      breadcrumbs.push({
        path: route.path,
        meta: { title: '功能测试计划详情', icon: 'material-symbols:assignment-turned-in' },
        clickable: false
      })
    } else if (route.path.includes('/test_plan/api_test_plan/detail/')) {
      // 接口测试计划详情页面
      breadcrumbs.push({
        path: '/test_plan/api_test_plan',
        meta: { title: '接口测试计划', icon: 'material-symbols:api' },
        clickable: true
      })
      breadcrumbs.push({
        path: route.path,
        meta: { title: '接口测试计划详情', icon: 'material-symbols:api' },
        clickable: false
      })
    } else if (route.path.includes('/project/detail/')) {
      // 项目详情页面
      breadcrumbs.push({
        path: '/project/project_manage',
        meta: { title: '项目管理', icon: 'material-symbols:folder-managed' },
        clickable: true
      })
      breadcrumbs.push({
        path: route.path,
        meta: { title: '项目详情', icon: 'material-symbols:folder-open' },
        clickable: false
      })
    } else {
      // 其他详情页面，使用默认逻辑但确保正确的层级关系
      if (matched.length > 0) {
        const currentItem = matched[matched.length - 1]
        // 尝试从路径推断父级页面
        const pathParts = route.path.split('/')
        if (pathParts.length >= 3) {
          const parentPath = pathParts.slice(0, -2).join('/')
          breadcrumbs.push({
            path: parentPath,
            meta: { title: '返回列表', icon: 'material-symbols:arrow-back' },
            clickable: true
          })
        }
        breadcrumbs.push({
          path: route.path,
          meta: currentItem.meta,
          clickable: false
        })
      }
    }

    return breadcrumbs.length > 0 ? breadcrumbs : matched
  }

  return matched
})

function handleBreadClick(item) {
  // 如果是当前页面或者不可点击，则不处理
  if (item.path === route.path || item.clickable === false) return

  // 如果是带参数的路径，需要特殊处理
  if (item.path.includes('/:')) {
    // 对于详情页面的父级路径，直接跳转到列表页
    const cleanPath = item.path.replace(/\/:[^/]+/g, '')
    router.push(cleanPath)
  } else {
    router.push(item.path)
  }
}

function getIcon(meta) {
  if (meta?.customIcon) return renderCustomIcon(meta.customIcon, { size: 18 })
  if (meta?.icon) return renderIcon(meta.icon, { size: 18 })
  return null
}
</script>
