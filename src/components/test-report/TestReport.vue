<template>
  <div class="test-report">
    <!-- 报告头部 -->
    <NCard :title="`${planName ? '  ' + planName : ''}${reportTime ? ' : ' + reportTime : ''}`" class="report-header">
      <template #header-extra>
        <NSpace>
          <NTag :type="reportData.execution_result === 'success' ? 'success' : 'error'" size="large">
            {{ reportData.execution_result === 'success' ? '执行成功' : '执行失败' }}
          </NTag>
<!--          <NButton size="small" @click="$emit('close')">-->
<!--            <TheIcon icon="material-symbols:close" :size="16" />-->
<!--            关闭-->
<!--          </NButton>-->
        </NSpace>
      </template>
      
      <!-- 执行摘要 -->
      <div class="summary-grid">
        <div class="summary-item">
          <div class="summary-label">总用例数</div>
          <div class="summary-value">{{ reportData.total_cases }}</div>
        </div>
        <div class="summary-item success">
          <div class="summary-label">通过用例</div>
          <div class="summary-value">{{ reportData.passed_cases }}</div>
        </div>
        <div class="summary-item error">
          <div class="summary-label">失败用例</div>
          <div class="summary-value">{{ reportData.failed_cases }}</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">通过率</div>
          <div class="summary-value">{{ reportData.pass_rate }}%</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">执行时间</div>
          <div class="summary-value">{{ formatExecutionTime(reportData.execution_time) }}</div>
        </div>
      </div>
    </NCard>

    <!-- 用例详情 -->
    <NCard title="用例执行详情" class="case-details">
      <NDataTable
        :columns="columns"
        :data="reportData.case_results || []"
        :pagination="false"
        :bordered="false"
        size="small"
        :row-class-name="getRowClassName"
      />
    </NCard>

    <!-- 用例详情抽屉 -->
    <NDrawer
      v-model:show="drawerVisible"
      :width="800"
      placement="right"
      :mask-closable="true"
    >
      <NDrawerContent :title="`用例详情: ${selectedCase?.case_name}`">
        <div v-if="selectedCase" class="case-detail-content">
          <!-- 基本信息 -->
          <NCard title="基本信息" size="small" class="detail-card">
            <div class="detail-grid">
              <div class="detail-item">
                <span class="detail-label">用例编号:</span>
                <span class="detail-value">{{ selectedCase.case_id }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">执行结果:</span>
                <NTag :type="selectedCase.success ? 'success' : 'error'" size="small">
                  {{ selectedCase.success ? '通过' : '失败' }}
                </NTag>
              </div>
              <div class="detail-item">
                <span class="detail-label">状态码:</span>
                <span class="detail-value">{{ selectedCase.status_code }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">响应时间:</span>
                <span class="detail-value">{{ selectedCase.response_time }}ms</span>
              </div>
            </div>
          </NCard>

          <!-- 错误信息 -->
          <NCard v-if="selectedCase.error_message" title="错误信息" size="small" class="detail-card">
            <NAlert type="error" :show-icon="false">
              {{ selectedCase.error_message }}
            </NAlert>
          </NCard>

          <!-- 断言结果 -->
          <NCard v-if="selectedCase.assertion_results && selectedCase.assertion_results.length > 0" 
                 title="断言结果" size="small" class="detail-card">
            <div class="assertion-list">
              <div 
                v-for="(assertion, index) in selectedCase.assertion_results" 
                :key="index"
                class="assertion-item"
                :class="{ 'assertion-failed': !assertion.passed }"
              >
                <div class="assertion-header">
                  <NTag :type="assertion.passed ? 'success' : 'error'" size="small">
                    {{ assertion.passed ? '通过' : '失败' }}
                  </NTag>
                  <span class="assertion-type">{{ getAssertionTypeLabel(assertion.type) }}</span>
                  <span class="assertion-operator">{{ getAssertionOperatorLabel(assertion.operator) }}</span>
                </div>
                <div class="assertion-details">
                  <div v-if="assertion.expected_value" class="assertion-detail">
                    <span class="assertion-detail-label">期望值:</span>
                    <span class="assertion-detail-value">{{ assertion.expected_value }}</span>
                  </div>
                  <div v-if="assertion.actual_value !== undefined" class="assertion-detail">
                    <span class="assertion-detail-label">实际值:</span>
                    <span class="assertion-detail-value">{{ assertion.actual_value }}</span>
                  </div>
                  <div v-if="assertion.json_path" class="assertion-detail">
                    <span class="assertion-detail-label">JSON路径:</span>
                    <span class="assertion-detail-value">{{ assertion.json_path }}</span>
                  </div>
                  <div v-if="assertion.header_name" class="assertion-detail">
                    <span class="assertion-detail-label">响应头:</span>
                    <span class="assertion-detail-value">{{ assertion.header_name }}</span>
                  </div>
                  <div v-if="assertion.description" class="assertion-detail">
                    <span class="assertion-detail-label">描述:</span>
                    <span class="assertion-detail-value">{{ assertion.description }}</span>
                  </div>
                  <div v-if="assertion.error_message" class="assertion-detail error">
                    <span class="assertion-detail-label">错误信息:</span>
                    <span class="assertion-detail-value">{{ assertion.error_message }}</span>
                  </div>
                </div>
              </div>
            </div>
          </NCard>

          <!-- 响应数据 -->
          <NCard title="响应数据" size="small" class="detail-card">
            <NTabs type="line" size="small">
              <NTabPane name="headers" tab="响应头">
                <div class="response-content" style="position: relative;">
                  <NButton
                    quaternary
                    circle
                    size="small"
                    style="position: absolute; top: 8px; right: 8px; z-index: 1;"
                    @click="handleCopyResponse(selectedCase.response_headers)"
                  >
                    <TheIcon icon="material-symbols:content-copy" :size="16"/>
                  </NButton>
                  <pre>{{ JSON.stringify(selectedCase.response_headers || {}, null, 2) }}</pre>
                </div>
              </NTabPane>
              <NTabPane name="body" tab="响应体">
                <div class="response-content" style="position: relative;">
                  <NButton
                    quaternary
                    circle
                    size="small"
                    style="position: absolute; top: 8px; right: 8px; z-index: 1;"
                    @click="handleCopyResponse(selectedCase.response_body)"
                  >
                    <TheIcon icon="material-symbols:content-copy" :size="16"/>
                  </NButton>
                  <pre>{{ formatResponseBody(selectedCase.response_body) }}</pre>
                </div>
              </NTabPane>
            </NTabs>
          </NCard>
        </div>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { 
  NCard, NSpace, NTag, NButton, NDataTable, NDrawer, NDrawerContent, 
  NAlert, NTabs, NTabPane
} from 'naive-ui'
import TheIcon from '@/components/icon/TheIcon.vue'

const props = defineProps({
  reportData: {
    type: Object,
    required: true
  },
  planName: {
    type: String,
    default: ''
  },
  reportTime: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['close'])

// 抽屉相关
const drawerVisible = ref(false)
const selectedCase = ref(null)

// 断言类型和操作符映射
const assertionTypeMap = {
  'status_code': '状态码断言',
  'response_time': '响应时间断言',
  'json_path': 'JSON路径断言',
  'response_body': '响应体断言',
  'response_header': '响应头断言'
}

const assertionOperatorMap = {
  'equals': '等于',
  'not_equals': '不等于',
  'greater_than': '大于',
  'less_than': '小于',
  'in_range': '在范围内',
  'contains': '包含',
  'not_contains': '不包含',
  'regex': '正则匹配',
  'exists': '存在',
  'not_exists': '不存在'
}

// 表格列定义
const columns = [
  {
    title: '用例名称',
    key: 'case_name',
    width: 350,
    ellipsis: { tooltip: true }
  },
  {
    title: '执行结果',
    key: 'success',
    width: 100,
    render: (row) => {
      return h(NTag, {
        type: row.success ? 'success' : 'error',
        size: 'small'
      }, {
        default: () => row.success ? '通过' : '失败'
      })
    }
  },
  {
    title: '状态码',
    key: 'status_code',
    width: 80
  },
  {
    title: '响应时间',
    key: 'response_time',
    width: 100,
    render: (row) => `${row.response_time}ms`
  },
  {
    title: '断言结果',
    key: 'assertion_results',
    ellipsis: { tooltip: true },
    render: (row) => {
      if (!row.assertion_results || !Array.isArray(row.assertion_results)) {
        return '无断言'
      }
      const totalCount = row.assertion_results.length
      const passedCount = row.assertion_results.filter(a => a.passed).length
      const allPassed = passedCount === totalCount
      return `断言结果: ${allPassed ? '全部通过' : '部分失败'} (${passedCount}/${totalCount})`
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 80,
    render: (row) => {
      return h(NButton, {
        size: 'small',
        type: 'primary',
        text: true,
        onClick: () => showCaseDetail(row)
      }, {
        default: () => '详情'
      })
    }
  }
]

// 方法
const formatExecutionTime = (time) => {
  if (!time) return '0ms'
  if (time < 1000) return `${Math.round(time)}ms`
  return `${(time / 1000).toFixed(2)}s`
}

const getRowClassName = (row) => {
  return row.success ? 'success-row' : 'error-row'
}

const showCaseDetail = (caseData) => {
  selectedCase.value = caseData
  drawerVisible.value = true
}

const getAssertionTypeLabel = (type) => {
  return assertionTypeMap[type] || type
}

const getAssertionOperatorLabel = (operator) => {
  return assertionOperatorMap[operator] || operator
}

const formatResponseBody = (body) => {
  if (!body) return ''
  try {
    if (typeof body === 'object') {
      return JSON.stringify(body, null, 2)
    }
    if (typeof body === 'string') {
      // 尝试解析为JSON
      const parsed = JSON.parse(body)
      return JSON.stringify(parsed, null, 2)
    }
    return String(body)
  } catch (e) {
    // 如果不是有效的JSON，直接返回原始字符串
    return String(body)
  }
}

// 复制响应体内容
const handleCopyResponse = async (content) => {
  try {
    const textToCopy = typeof content === 'object' ? JSON.stringify(content, null, 2) : content
    await navigator.clipboard.writeText(textToCopy)
    // 这里可以添加消息提示，但需要引入message组件
    console.log('复制成功')
  } catch (err) {
    console.error('复制失败:', err)
  }
}
</script>

<style scoped>
.test-report {
  padding: 16px;
}

.report-header {
  margin-bottom: 16px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.summary-item {
  text-align: center;
  padding: 16px;
  border-radius: 6px;
  background: var(--n-color);
  border: 1px solid var(--n-border-color);
}

.summary-item.success {
  background: rgba(24, 160, 88, 0.1);
  border-color: rgba(24, 160, 88, 0.3);
}

.summary-item.error {
  background: rgba(208, 48, 80, 0.1);
  border-color: rgba(208, 48, 80, 0.3);
}

.summary-label {
  font-size: 12px;
  color: var(--n-text-color-2);
  margin-bottom: 4px;
}

.summary-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.case-details {
  margin-bottom: 16px;
}

.case-detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-label {
  font-weight: 500;
  color: var(--n-text-color-2);
  min-width: 80px;
}

.detail-value {
  color: var(--n-text-color-1);
}

.assertion-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.assertion-item {
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--n-border-color);
  background: var(--n-color);
}

.assertion-item.assertion-failed {
  background: rgba(208, 48, 80, 0.05);
  border-color: rgba(208, 48, 80, 0.2);
}

.assertion-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.assertion-type {
  font-weight: 500;
  color: var(--n-text-color-1);
}

.assertion-operator {
  color: var(--n-text-color-2);
}

.assertion-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.assertion-detail {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 12px;
}

.assertion-detail.error {
  color: var(--n-error-color);
}

.assertion-detail-label {
  font-weight: 500;
  color: var(--n-text-color-2);
  min-width: 60px;
  flex-shrink: 0;
}

.assertion-detail-value {
  color: var(--n-text-color-1);
  word-break: break-all;
}

.response-content {
  max-height: 300px;
  overflow: auto;
}

.response-content pre {
  margin: 0;
  padding: 12px;
  background: var(--n-code-color);
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.success-row) {
  background: rgba(24, 160, 88, 0.05);
}

:deep(.error-row) {
  background: rgba(208, 48, 80, 0.05);
}
</style>
