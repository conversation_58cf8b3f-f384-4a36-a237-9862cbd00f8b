<template>
  <div class="faker-data-page">
    <n-card title="随机数据生成器" size="small">
      <template #header-extra>
        <n-button type="primary" @click="refreshAllSamples" :loading="loading">
          <component :is="renderIcon('mdi:refresh', { size: 16 })" />
          刷新所有示例
        </n-button>
      </template>

      <n-alert type="info" style="margin-bottom: 16px;">
        <template #icon>
          <component :is="renderIcon('mdi:information', { size: 16 })" />
        </template>
        在接口测试用例和测试计划中，您可以使用 <n-tag type="success">${{变量名}}</n-tag> 的格式来引用这些随机数据。
      </n-alert>

      <!-- 加载状态 -->
      <div v-if="loading" style="text-align: center; padding: 40px;">
        <n-spin size="large" />
        <div style="margin-top: 16px;">正在加载示例数据...</div>
      </div>

      <!-- 数据为空时的提示 -->
      <div v-else-if="Object.keys(sampleData).length === 0" style="text-align: center; padding: 40px;">
        <n-empty description="暂无数据">
          <template #extra>
            <n-button @click="fetchSampleData">重新加载</n-button>
          </template>
        </n-empty>
      </div>

      <!-- 数据内容 -->
      <n-tabs v-else type="line" animated>
        <n-tab-pane
          v-for="(categoryData, categoryKey) in sampleData"
          :key="categoryKey"
          :name="categoryKey"
          :tab="getCategoryName(categoryKey)"
        >
          <n-grid :cols="24" :x-gap="16" :y-gap="16">
            <n-grid-item
              v-for="(item, key) in categoryData"
              :key="key"
              :span="12"
            >
              <n-card size="small" hoverable>
                <template #header>
                  <div class="data-item-header">
                    <n-tag type="primary" size="small">{{ item.description }}</n-tag>
                    <n-button
                      size="tiny"
                      type="tertiary"
                      @click="refreshSample(categoryKey, key)"
                    >
                      <component :is="renderIcon('mdi:refresh', { size: 14 })" />
                    </n-button>
                  </div>
                </template>

                <div class="data-item-content">
                  <div class="variable-name">
                    <n-text depth="3">变量名：</n-text>
                    <n-tag
                      type="success"
                      size="small"
                      @click="copyToClipboard(item.variable)"
                      style="cursor: pointer;"
                    >
                      {{ item.variable }}
                    </n-tag>
                  </div>

                  <div class="sample-value">
                    <n-text depth="3">示例值：</n-text>
                    <n-text
                      code
                      @click="copyToClipboard(item.sample)"
                      style="cursor: pointer; user-select: all;"
                    >
                      {{ item.sample }}
                    </n-text>
                  </div>
                </div>
              </n-card>
            </n-grid-item>
          </n-grid>
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { NCard, NTabs, NTabPane, NGrid, NGridItem, NTag, NText, NButton, NIcon, NAlert, NSpin, NEmpty, useMessage } from 'naive-ui'
import { renderIcon } from '@/utils'
import { request } from '@/utils'

defineOptions({ name: 'TopMenu' })

const message = useMessage()
const sampleData = ref({})
const loading = ref(false)

// 分类名称映射
const categoryNames = {
  personal: '个人信息',
  contact: '联系信息',
  company: '公司信息',
  internet: '网络信息',
  datetime: '日期时间',
  number: '数字',
  text: '文本',
  finance: '金融',
  color: '颜色',
  file: '文件'
}

const getCategoryName = (key) => {
  return categoryNames[key] || key
}

// 获取示例数据
const fetchSampleData = async () => {
  try {
    console.log('开始获取 Faker 示例数据...')
    loading.value = true
    const response = await request.get('/faker_data/sample_data')
    console.log('API 响应:', response)
    if (response.code === 200) {
      sampleData.value = response.data
      console.log('示例数据设置成功:', sampleData.value)
    } else {
      console.error('API 返回错误:', response)
      message.error(response.msg || '获取示例数据失败')
    }
  } catch (error) {
    console.error('获取示例数据失败:', error)
    message.error('获取示例数据失败')
  } finally {
    loading.value = false
  }
}

// 刷新单个示例
const refreshSample = async (category, dataType) => {
  try {
    const response = await request.post('/faker_data/generate', null, {
      params: {
        data_type: dataType,
        category: category,
        count: 1
      }
    })

    if (response.code === 200 && response.data.results.length > 0) {
      if (sampleData.value[category] && sampleData.value[category][dataType]) {
        sampleData.value[category][dataType].sample = response.data.results[0]
      }
    } else {
      message.error('刷新示例失败')
    }
  } catch (error) {
    console.error('刷新示例失败:', error)
    message.error('刷新示例失败')
  }
}

// 刷新所有示例
const refreshAllSamples = () => {
  fetchSampleData()
  message.success('已刷新所有示例数据')
}

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    message.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

onMounted(() => {
  console.log('Faker 数据生成器页面已挂载')
  fetchSampleData()
})
</script>

<style scoped>
.faker-data-page {
  padding: 16px;
}

.data-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-item-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.variable-name,
.sample-value {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.variable-name .n-tag,
.sample-value .n-text {
  transition: all 0.3s ease;
}

.variable-name .n-tag:hover,
.sample-value .n-text:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
