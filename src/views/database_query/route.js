const Layout = () => import('@/layout/index.vue')

export default {
  name: 'database_query',
  path: '/database_query',
  component: Layout,
  redirect: '/database_query/connection_manage',
  meta: {
    title: '数据库查询',
    icon: 'mdi:database',
    order: 8
  },
  children: [
    {
      name: 'database_query_connection_manage',
      path: '/database_query/connection_manage',
      component: () => import('./connection_manage/index.vue'),
      meta: {
        title: '连接管理',
        icon: 'mdi:database-settings',
        keepAlive: true
      }
    },
    {
      name: 'database_query_sql_executor',
      path: '/database_query/sql_executor',
      component: () => import('./sql_executor/index.vue'),
      meta: {
        title: 'SQL执行器',
        icon: 'mdi:database-search',
        keepAlive: true
      }
    },
    {
      name: 'database_query_history',
      path: '/database_query/history',
      component: () => import('./query_history/index.vue'),
      meta: {
        title: '查询历史',
        icon: 'mdi:history',
        keepAlive: true
      }
    }
  ]
}
