<template>
  <CommonPage show-footer :title="`${projectDetail?.name || '项目'} - 模块管理`">
    <template #action>
      <NSpace>
        <NButton @click="goBack">
          <TheIcon icon="material-symbols:arrow-back" :size="18" class="mr-5"/>
          返回
        </NButton>
        <NButton type="primary" @click="handleAdd">
          <TheIcon icon="material-symbols:add" :size="18" class="mr-1" />
          新建模块
        </NButton>
      </NSpace>
    </template>

    <!-- 项目基本信息 -->
    <NCard title="项目信息" size="small" style="margin-bottom: 16px;" v-if="projectDetail">
      <NDescriptions :column="3" label-placement="left">
        <NDescriptionsItem label="项目名称">{{ projectDetail.name }}</NDescriptionsItem>
        <NDescriptionsItem label="项目状态">
          <NTag :type="getStatusType(projectDetail.status)">
            {{ getStatusText(projectDetail.status) }}
          </NTag>
        </NDescriptionsItem>
        <NDescriptionsItem label="项目经理">{{ projectDetail.manager || '未指定' }}</NDescriptionsItem>
        <NDescriptionsItem label="开始日期">{{ formatDate(projectDetail.start_date) || '未设置' }}</NDescriptionsItem>
        <NDescriptionsItem label="结束日期">{{ formatDate(projectDetail.end_date) || '未设置' }}</NDescriptionsItem>
        <NDescriptionsItem label="预算">{{ projectDetail.budget || 0 }} 万元</NDescriptionsItem>
        <NDescriptionsItem label="项目描述" :span="3">{{ projectDetail.description || '无描述' }}</NDescriptionsItem>
      </NDescriptions>
    </NCard>

    <!-- 模块管理 -->
    <NCard title="模块管理" size="small">
      <template #header-extra>
        <NSpace>
          <NInput
            v-model:value="searchName"
            placeholder="搜索模块名称"
            clearable
            style="width: 200px"
            @keypress.enter="loadModuleTree"
          />
          <NButton @click="loadModuleTree" :loading="loading">
            <TheIcon icon="material-symbols:search" :size="16" />
            搜索
          </NButton>
          <NButton @click="resetSearch">
            <TheIcon icon="material-symbols:refresh" :size="16" />
            重置
          </NButton>
        </NSpace>
      </template>

      <NTree
        v-if="moduleTreeData.length > 0"
        :data="moduleTreeData"
        key-field="id"
        label-field="name"
        children-field="children"
        block-line
        expand-on-click
        :render-suffix="renderTreeSuffix"
        :loading="loading"
      />
      <NEmpty v-else-if="!loading" description="暂无模块数据" />
      <NSpin v-else />
    </NCard>

    <!-- 新增/编辑模块弹窗 -->
    <NModal v-model:show="modalVisible" preset="dialog" :title="modalTitle" style="width: 600px;">
      <NForm ref="formRef" :model="formData" :rules="formRules" label-placement="left" :label-width="80">
        <NFormItem label="模块名称" path="name">
          <NInput v-model:value="formData.name" placeholder="请输入模块名称" />
        </NFormItem>
        <NFormItem label="模块描述" path="description">
          <NInput
            v-model:value="formData.description"
            type="textarea"
            placeholder="请输入模块描述"
            :rows="3"
          />
        </NFormItem>
        <NFormItem label="父级模块" path="parent_id">
          <NTreeSelect
            v-model:value="formData.parent_id"
            :options="moduleSelectOptions"
            key-field="id"
            label-field="name"
            children-field="children"
            placeholder="请选择父级模块（可选）"
            clearable
            default-expand-all
          />
        </NFormItem>
        <NFormItem label="排序" path="order">
          <NInputNumber v-model:value="formData.order" placeholder="排序值" :min="0" />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSelect
            v-model:value="formData.status"
            :options="statusOptions"
            placeholder="请选择状态"
          />
        </NFormItem>
      </NForm>
      <template #action>
        <NSpace>
          <NButton @click="modalVisible = false">取消</NButton>
          <NButton type="primary" @click="handleSave" :loading="saveLoading">保存</NButton>
        </NSpace>
      </template>
    </NModal>
  </CommonPage>
</template>

<script setup>
import { ref, reactive, onMounted, computed, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { NButton, NPopconfirm, NSpace, NTag } from 'naive-ui'
import CommonPage from '@/components/page/CommonPage.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import { formatDate } from '@/utils'
import projectApi from '@/api/project'

defineOptions({ name: '项目详情' })

const route = useRoute()
const router = useRouter()
const message = useMessage()

// 响应式数据
const projectId = ref(parseInt(route.params.id))
const projectDetail = ref(null)
const moduleTreeData = ref([])
const loading = ref(false)
const searchName = ref('')

// 弹窗相关
const modalVisible = ref(false)
const modalTitle = ref('')
const modalAction = ref('')
const saveLoading = ref(false)
const formRef = ref(null)

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  description: '',
  project_id: projectId.value,
  parent_id: 0,
  order: 0,
  status: 'active'
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入模块名称', trigger: 'blur' }
  ]
}

// 状态选项
const statusOptions = [
  { label: '激活', value: 'active' },
  { label: '禁用', value: 'inactive' }
]

// 状态类型映射
const getStatusType = (status) => {
  const typeMap = {
    'planning': 'info',
    'in_progress': 'warning', 
    'completed': 'success',
    'cancelled': 'error'
  }
  return typeMap[status] || 'default'
}

// 状态文本映射
const getStatusText = (status) => {
  const textMap = {
    'planning': '规划中',
    'in_progress': '进行中',
    'completed': '已完成', 
    'cancelled': '已取消'
  }
  return textMap[status] || status
}

// 模块选择选项（用于父级模块选择）
const moduleSelectOptions = computed(() => {
  const addRootOption = (data) => {
    return [
      { id: 0, name: '根模块', children: data }
    ]
  }
  return addRootOption(moduleTreeData.value)
})

// 获取项目详情
const getProjectDetail = async () => {
  try {
    const response = await projectApi.getProject({ project_id: projectId.value })
    projectDetail.value = response.data
  } catch (error) {
    console.error('获取项目详情失败:', error)
    message.error('获取项目详情失败: ' + error.message)
  }
}

// 加载模块树
const loadModuleTree = async () => {
  try {
    loading.value = true
    const params = {
      project_id: projectId.value
    }
    if (searchName.value) {
      params.name = searchName.value
    }

    const response = await projectApi.getProjectModuleTree(params)
    moduleTreeData.value = response.data || []
  } catch (error) {
    console.error('加载模块树失败:', error)
    message.error('加载模块树失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchName.value = ''
  loadModuleTree()
}

// 返回上一页
const goBack = () => {
  router.push('/project/project_manage')
}

// 新增模块
const handleAdd = () => {
  modalAction.value = 'create'
  modalTitle.value = '新建模块'
  Object.assign(formData, {
    id: null,
    name: '',
    description: '',
    project_id: projectId.value,
    parent_id: 0,
    order: 0,
    status: 'active'
  })
  modalVisible.value = true
}

// 编辑模块
const handleEdit = (row) => {
  modalAction.value = 'edit'
  modalTitle.value = '编辑模块'
  Object.assign(formData, {
    id: row.id,
    name: row.name,
    description: row.description || '',
    project_id: row.project_id,
    parent_id: row.parent_id,
    order: row.order,
    status: row.status
  })
  modalVisible.value = true
}

// 删除模块
const handleDelete = async (row) => {
  try {
    await projectApi.deleteProjectModule({ module_id: row.id })
    message.success('删除成功')
    await loadModuleTree()
  } catch (error) {
    message.error('删除失败: ' + error.message)
  }
}

// 保存模块
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    saveLoading.value = true

    if (modalAction.value === 'create') {
      await projectApi.createProjectModule(formData)
      message.success('创建成功')
    } else {
      await projectApi.updateProjectModule(formData)
      message.success('更新成功')
    }

    modalVisible.value = false
    await loadModuleTree()
  } catch (error) {
    if (error.message) {
      message.error('保存失败: ' + error.message)
    }
  } finally {
    saveLoading.value = false
  }
}

// 树节点后缀渲染
const renderTreeSuffix = ({ option }) => {
  return h(NSpace, { size: 'small' }, {
    default: () => [
      h(NButton, {
        size: 'small',
        type: 'primary',
        secondary: true,
        onClick: () => handleEdit(option)
      }, { default: () => '编辑' }),
      h(NPopconfirm, {
        onPositiveClick: () => handleDelete(option)
      }, {
        default: () => '确定删除吗？',
        trigger: () => h(NButton, {
          size: 'small',
          type: 'error',
          secondary: true
        }, { default: () => '删除' })
      })
    ]
  })
}

// 页面初始化
onMounted(async () => {
  await getProjectDetail()
  await loadModuleTree()
})
</script>

<style scoped>
.project-detail-page {
  padding: 16px;
}
</style>
